<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Animate Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="text-white">
    <div class="container mx-auto px-4 py-16">
        <div class="text-center space-y-8">
            <!-- Test Custom Motion Variants -->
            <h1 class="text-4xl md:text-6xl font-black text-white leading-tight text-animate-container">
                <span class="block" 
                      data-text-animate="customMotion" 
                      data-delay="0.2" 
                      data-duration="0.8"
                      data-by="word">Bangun Rumah</span>
                <span class="block text-yellow-400" 
                      data-text-animate="customMotion" 
                      data-delay="0.6" 
                      data-duration="0.8"
                      data-by="word">I<PERSON>ian <PERSON><PERSON></span>
            </h1>
            
            <!-- Test Blur In by Text -->
            <p class="text-lg md:text-xl text-gray-200 max-w-3xl mx-auto leading-relaxed" 
               data-text-animate="blurInText" 
               data-delay="1.0" 
               data-duration="1.2"
               data-by="text">
                Desain modern, konstruksi berkualitas, dan solusi berkelanjutan untuk mewujudkan hunian ideal sesuai gaya hidup Anda.
            </p>
            
            <!-- Additional Tests -->
            <div class="mt-16 space-y-4">
                <h2 class="text-2xl font-bold" 
                    data-text-animate="blurInUp" 
                    data-delay="2.0" 
                    data-by="word">Additional Animation Tests</h2>
                
                <p class="text-base text-gray-300" 
                   data-text-animate="customMotion" 
                   data-delay="2.5" 
                   data-by="character">Character by character animation</p>
            </div>
        </div>
    </div>
    
    <!-- Load the text-animate script -->
    <script src="public/assets/js/text-animate.js"></script>
    
    <!-- Debug information -->
    <script>
        // Add some debug information
        window.addEventListener('load', () => {
            console.log('Page loaded, TextAnimate should be initialized');
            console.log('Available presets:', window.ANIMATION_PRESETS);
            
            // Check if animations are working
            setTimeout(() => {
                const animatedElements = document.querySelectorAll('[data-text-animate]');
                console.log(`Found ${animatedElements.length} elements with text animations`);
                
                animatedElements.forEach((el, index) => {
                    const spans = el.querySelectorAll('span');
                    console.log(`Element ${index}: ${spans.length} spans created`);
                });
            }, 1000);
        });
    </script>
</body>
</html>
