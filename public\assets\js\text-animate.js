/**
 * Magic UI Text Animate - Vanilla JavaScript Implementation
 * Replicates Magic UI text-animate functionality for PHP-based projects
 * Supports Custom Motion Variants and Blur In by Text animations
 * Enhanced with accessibility and performance optimizations
 */

class TextAnimate {
    constructor(element, options = {}) {
        this.element = element;

        // Respect user's motion preferences
        this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        this.options = {
            animation: options.animation || 'fadeIn',
            by: options.by || 'word',
            delay: options.delay || 0,
            duration: this.prefersReducedMotion ? 0.1 : (options.duration || 0.3),
            startOnView: options.startOnView !== false,
            once: options.once !== false,
            stagger: this.prefersReducedMotion ? 0 : (options.stagger || 0.1),
            variants: options.variants || null,
            respectMotionPreference: options.respectMotionPreference !== false,
            ...options
        };

        this.originalText = element.textContent.trim();
        this.isAnimated = false;
        this.observer = null;
        this.spans = [];

        // Early return if reduced motion is preferred and respect is enabled
        if (this.prefersReducedMotion && this.options.respectMotionPreference) {
            this.element.style.opacity = '1';
            return;
        }

        this.init();
    }
    
    init() {
        try {
            this.prepareElement();

            if (this.options.startOnView) {
                this.setupIntersectionObserver();
            } else {
                setTimeout(() => this.animate(), this.options.delay * 1000);
            }
        } catch (error) {
            console.warn('TextAnimate initialization failed:', error);
            // Fallback: ensure element is visible
            this.element.style.opacity = '1';
        }
    }
    
    prepareElement() {
        // Clear the element and prepare for animation
        this.element.innerHTML = '';
        this.element.style.opacity = '1';
        
        // Split text based on 'by' option
        const parts = this.splitText(this.originalText, this.options.by);
        
        // Create spans for each part
        this.spans = parts.map((part, index) => {
            const span = document.createElement('span');
            span.textContent = part;
            span.style.display = this.options.by === 'line' ? 'block' : 'inline-block';
            span.style.opacity = '0';
            span.style.transform = this.getInitialTransform();
            span.style.filter = this.getInitialFilter();
            span.style.transition = `all ${this.options.duration}s ease-out`;
            span.style.transitionDelay = `${index * this.options.stagger}s`;
            
            // Add space after word (except for last word)
            if (this.options.by === 'word' && index < parts.length - 1) {
                span.style.marginRight = '0.25em';
            }
            
            this.element.appendChild(span);
            return span;
        });
    }
    
    splitText(text, by) {
        switch (by) {
            case 'character':
                return text.split('');
            case 'word':
                return text.split(/\s+/);
            case 'line':
                return text.split('\n');
            case 'text':
            default:
                return [text];
        }
    }
    
    getInitialTransform() {
        const variants = this.options.variants;
        if (variants && variants.hidden && variants.hidden.transform) {
            return variants.hidden.transform;
        }
        
        switch (this.options.animation) {
            case 'blurInUp':
                return 'translateY(20px)';
            case 'slideUp':
                return 'translateY(30px)';
            case 'slideLeft':
                return 'translateX(-20px)';
            case 'scaleUp':
                return 'scale(0.8)';
            case 'customMotion':
                return 'translateY(40px) rotateX(90deg)';
            default:
                return 'translateY(10px)';
        }
    }
    
    getInitialFilter() {
        const variants = this.options.variants;
        if (variants && variants.hidden && variants.hidden.filter) {
            return variants.hidden.filter;
        }
        
        switch (this.options.animation) {
            case 'blurInUp':
            case 'blurInText':
                return 'blur(10px)';
            default:
                return 'none';
        }
    }
    
    getFinalTransform() {
        const variants = this.options.variants;
        if (variants && variants.visible && variants.visible.transform) {
            return variants.visible.transform;
        }
        return 'translateY(0) translateX(0) scale(1) rotateX(0)';
    }
    
    getFinalFilter() {
        const variants = this.options.variants;
        if (variants && variants.visible && variants.visible.filter) {
            return variants.visible.filter;
        }
        return 'blur(0px)';
    }
    
    setupIntersectionObserver() {
        // Check if IntersectionObserver is supported
        if (!window.IntersectionObserver) {
            console.warn('TextAnimate: IntersectionObserver not supported, falling back to immediate animation');
            setTimeout(() => this.animate(), this.options.delay * 1000);
            return;
        }

        const options = {
            threshold: 0.1,
            rootMargin: '0px 0px -10% 0px'
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isAnimated) {
                    setTimeout(() => this.animate(), this.options.delay * 1000);
                    if (this.options.once) {
                        this.observer.disconnect();
                    }
                }
            });
        }, options);

        this.observer.observe(this.element);
    }
    
    animate() {
        if (this.isAnimated) return;
        this.isAnimated = true;
        
        this.spans.forEach((span, index) => {
            setTimeout(() => {
                span.style.opacity = '1';
                span.style.transform = this.getFinalTransform();
                span.style.filter = this.getFinalFilter();
                
                // Add custom motion for special animations
                if (this.options.animation === 'customMotion') {
                    span.style.transformOrigin = 'center bottom';
                    span.style.animation = `customWave 0.6s ease-out ${index * this.options.stagger}s forwards`;
                }
            }, index * this.options.stagger * 1000);
        });
    }
    
    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        this.element.innerHTML = this.originalText;
    }
}

// Predefined animation presets
const ANIMATION_PRESETS = {
    blurInText: {
        animation: 'blurInText',
        by: 'text',
        duration: 0.8,
        stagger: 0
    },
    blurInUp: {
        animation: 'blurInUp',
        by: 'word',
        duration: 0.5,
        stagger: 0.1
    },
    customMotion: {
        animation: 'customMotion',
        by: 'word',
        duration: 0.6,
        stagger: 0.15,
        variants: {
            hidden: {
                transform: 'translateY(40px) rotateX(90deg)',
                filter: 'blur(4px)'
            },
            visible: {
                transform: 'translateY(0) rotateX(0)',
                filter: 'blur(0px)'
            }
        }
    }
};

// CSS animations for custom motion with accessibility support
const customAnimationCSS = `
/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
    .text-animate-container *,
    .text-animate-container *::before,
    .text-animate-container *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

@keyframes customWave {
    0% {
        transform: translateY(40px) rotateX(90deg);
        filter: blur(4px);
        opacity: 0;
    }
    50% {
        transform: translateY(-5px) rotateX(0deg);
        filter: blur(1px);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0) rotateX(0deg);
        filter: blur(0px);
        opacity: 1;
    }
}

.text-animate-container {
    perspective: 1000px;
    transform-style: preserve-3d;
}

/* Performance optimizations */
.text-animate-container span {
    will-change: transform, opacity, filter;
    backface-visibility: hidden;
    transform-style: preserve-3d;
}

/* Ensure text remains accessible during animation */
.text-animate-container[aria-hidden="true"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    width: 1px;
    height: 1px;
    overflow: hidden;
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = customAnimationCSS;
document.head.appendChild(style);

// Auto-initialize elements with data-text-animate attribute
document.addEventListener('DOMContentLoaded', () => {
    try {
        const elements = document.querySelectorAll('[data-text-animate]');

        if (elements.length === 0) {
            console.info('TextAnimate: No elements found with data-text-animate attribute');
            return;
        }

        elements.forEach((element, index) => {
            try {
                const preset = element.getAttribute('data-text-animate');
                const options = ANIMATION_PRESETS[preset] || {};

                // Parse additional options from data attributes with validation
                const customOptions = {
                    delay: Math.max(0, parseFloat(element.getAttribute('data-delay')) || options.delay || 0),
                    duration: Math.max(0.1, parseFloat(element.getAttribute('data-duration')) || options.duration || 0.3),
                    by: element.getAttribute('data-by') || options.by || 'word'
                };

                // Add small delay between initializations to prevent performance issues
                setTimeout(() => {
                    new TextAnimate(element, { ...options, ...customOptions });
                }, index * 50);

            } catch (error) {
                console.warn(`TextAnimate: Failed to initialize element ${index}:`, error);
                // Ensure element remains visible on error
                element.style.opacity = '1';
            }
        });

        console.info(`TextAnimate: Successfully initialized ${elements.length} elements`);

    } catch (error) {
        console.error('TextAnimate: Critical initialization error:', error);
    }
});

// Export for manual initialization
window.TextAnimate = TextAnimate;
window.ANIMATION_PRESETS = ANIMATION_PRESETS;

// Add performance monitoring (optional)
if (typeof performance !== 'undefined' && performance.mark) {
    performance.mark('text-animate-loaded');
}
